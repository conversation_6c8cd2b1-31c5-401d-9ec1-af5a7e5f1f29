ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)
startGeminiVoiceCall @ guest_dashboard_voice_call.js:249Understand this warning
guest_dashboard_voice_call.js:249 [Deprecation] The ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)
startGeminiVoiceCall @ guest_dashboard_voice_call.js:249Understand this warning
guest_dashboard_voice_call.js:249 [Deprecation] The ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)
startGeminiVoiceCall @ guest_dashboard_voice_call.js:249Understand this warning
guest_dashboard_voice_call.js:249 [Deprecation] The ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)
startGeminiVoiceCall @ guest_dashboard_voice_call.js:249Understand this warning
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 86)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1931 📝 Fragment (ai): " is"
guest_dashboard_voice_call.js:1424 🔊 Noise analysis: avg=0.0011, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:576 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 96)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1105 Converting base64 to ArrayBuffer (length: 2560)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1286 Scheduled audio chunk (0.040s) to play at 1705.237, queue length: 11
guest_dashboard_voice_call.js:1286 Scheduled audio chunk (0.040s) to play at 1705.277, queue length: 10
4guest_dashboard_voice_call.js:1307 Audio chunk playback complete
2guest_dashboard_voice_call.js:576 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1105 Converting base64 to ArrayBuffer (length: 2560)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1424 🔊 Noise analysis: avg=0.0010, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:2213 📝 User Voice Complete: うん 。 me
guest_dashboard_voice_call.js:2121 No active voice conversation session, cannot store message
storeVoiceMessage @ guest_dashboard_voice_call.js:2121Understand this warning
guest_dashboard_voice_call.js:2192 🎯 Displayed user voice transcription in chat UI
guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_text_chat.js:177 Adding date separator for message: user 05:27 PM
dashboard:2075 addDateSeparatorIfNeeded: Using stored timestamp 2025-06-25T22:27:51.186Z -> Wed Jun 25 2025
dashboard:2096 addDateSeparatorIfNeeded: Previous message timestamp 2025-06-25T22:27:43.275Z -> Wed Jun 25 2025
dashboard:2123 No date separator needed for message
4guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_voice_call.js:1424 🔊 Noise analysis: avg=0.0012, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:576 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_voice_call.js:576 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_voice_call.js:576 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1105 Converting base64 to ArrayBuffer (length: 2560)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:1286 Scheduled audio chunk (0.040s) to play at 1707.117, queue length: 16
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1105 Converting base64 to ArrayBuffer (length: 2560)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1286 Scheduled audio chunk (0.040s) to play at 1707.197, queue length: 25
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1105 Converting base64 to ArrayBuffer (length: 2560)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 89)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 90)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1931 📝 Fragment (ai): " password"
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1105 Converting base64 to ArrayBuffer (length: 2560)
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:1286 Scheduled audio chunk (0.040s) to play at 1708.597, queue length: 36
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 99)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1132 Converted base64 to ArrayBuffer: 1920 bytes
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:576 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1159 Queueing audio for playback (1920 bytes, ~960 samples, ~0.04s duration)
guest_dashboard_voice_call.js:424 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:425 Blob MIME type: unknown
guest_dashboard_voice_call.js:438 Parsed JSON from Blob: Object
guest_dashboard_voice_call.js:1615 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:123 Voice call button clicked, current state: active
guest_dashboard_voice_call.js:126 === PROPERTY ID DEBUG INFO ===
guest_dashboard_voice_call.js:127 confirmedPropertyId (imported): 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:128 window.PROPERTY_ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:129 document.body.dataset.propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:130 window.propertyDetails: Available
guest_dashboard_voice_call.js:197 User initiated call end.
guest_dashboard_voice_call.js:599 Stopping voice call. Reason: User ended call
guest_dashboard_voice_call.js:637 Microphone stream stopped.
guest_dashboard_voice_call.js:719 🔇 Stopping all audio playback immediately
guest_dashboard_voice_call.js:770 🗑️ Dropped 51 audio chunks
guest_dashboard_voice_call.js:773 ✅ All audio playback stopped successfully
guest_dashboard_voice_call.js:1587 🔇 Stopped noise monitoring and reset state
guest_dashboard_voice_call.js:698 🎤 AI Complete (final): Hi Andrew!Welcome to Chicago Place! I can help you with the Wi-Fi information. The network name is "ATT5FxQ4Kp" and the password is "4f2e92#ze=gc".
guest_dashboard_voice_call.js:2213 📝 AI Voice Complete: Hi Andrew!Welcome to Chicago Place! I can help you with the Wi-Fi information. The network name is "ATT5FxQ4Kp" and the password is "4f2e92#ze=gc".
guest_dashboard_voice_call.js:2121 No active voice conversation session, cannot store message
storeVoiceMessage @ guest_dashboard_voice_call.js:2121Understand this warning
guest_dashboard_voice_call.js:2192 🎯 Displayed assistant voice transcription in chat UI
7guest_dashboard_voice_call.js:1307 Audio chunk playback complete
guest_dashboard_text_chat.js:177 Adding date separator for message: assistant 05:27 PM
dashboard:2075 addDateSeparatorIfNeeded: Using stored timestamp 2025-06-25T22:27:54.240Z -> Wed Jun 25 2025
dashboard:2096 addDateSeparatorIfNeeded: Previous message timestamp 2025-06-25T22:27:51.186Z -> Wed Jun 25 2025
dashboard:2123 No date separator needed for message
guest_dashboard_text_chat.js:177 Adding date separator for message: assistant 05:27 PM
dashboard:2075 addDateSeparatorIfNeeded: Using stored timestamp 2025-06-25T22:27:54.247Z -> Wed Jun 25 2025
dashboard:2096 addDateSeparatorIfNeeded: Previous message timestamp 2025-06-25T22:27:54.240Z -> Wed Jun 25 2025
dashboard:2123 No date separator needed for message
guest_dashboard_voice_call.js:475 WebSocket closed. Code: 1000, Reason: 
guest_dashboard_text_chat.js:911 Chat not connected, starting automatically...
guest_dashboard_text_chat.js:954 Auto-starting chat with URL: https://dev.guestrix.ai/
guest_dashboard_text_chat.js:466 initSocket called with: Object
guest_dashboard_utils.js:184 Using cached property details for 1a344329-2670-4b34-a4f6-e28513a3200c from dashboardState.propertyCache
guest_dashboard_reservations.js:798 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_reservations.js:816 Updated reservation card for property 1a344329-2670-4b34-a4f6-e28513a3200c with name: unknown, address: unknown
guest_dashboard_utils.js:375 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:493 Verified property details before socket connection: Object
guest_dashboard_text_chat.js:497 Fetching knowledge items for text chat...
guest_dashboard_utils.js:416 Using cached knowledge items for property 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:504 Knowledge items fetched successfully for text chat
guest_dashboard_text_chat.js:518 Socket.IO connection strategy: Using URL https://dev.guestrix.ai/
guest_dashboard_text_chat.js:519 Using auth token: eyJhbGciOi...
guest_dashboard_text_chat.js:520 Using property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:532 Guest name for Socket.IO auth: Andrew
guest_dashboard_text_chat.js:542 Extracted user ID from token: temp_magic_9daee134452a
guest_dashboard_text_chat.js:563 Converted WebSocket URL to HTTP/HTTPS for Socket.IO: https://dev.guestrix.ai/
guest_dashboard_text_chat.js:177 Adding date separator for message: user 05:28 PM
dashboard:2075 addDateSeparatorIfNeeded: Using stored timestamp 2025-06-25T22:28:15.512Z -> Wed Jun 25 2025
dashboard:2096 addDateSeparatorIfNeeded: Previous message timestamp 2025-06-25T22:27:54.247Z -> Wed Jun 25 2025
dashboard:2123 No date separator needed for message
guest_dashboard_text_chat.js:680 Socket.IO connection successful: Object
guest_dashboard_text_chat.js:596 Socket.IO connection OPENED
guest_dashboard_text_chat.js:1012 Chat connection status: Connected
guest_dashboard_utils.js:572 Creating shared system prompt for property: Chicago Place
guest_dashboard_utils.js:573 Property details available: Yes
guest_dashboard_utils.js:574 Knowledge items available: Yes
guest_dashboard_utils.js:600 Using knowledgeItems from dashboardState
guest_dashboard_utils.js:654 Added reservation context to system prompt: 

Reservation Details:
Check-in: Jun 2, 2025
Check-out: Aug 10, 2025

guest_dashboard_utils.js:697 Created shared system prompt
guest_dashboard_utils.js:698 Prompt length: 14167
guest_dashboard_text_chat.js:614 Found reservation ID from current reservation: 5456e1c8-36ce-4a8d-9bfe-139f7cab5f30
guest_dashboard_text_chat.js:631 Phone number for auth: ***-***-6804
guest_dashboard_text_chat.js:662 Sent auth message with user_id, property_id, and reservation_id: 5456e1c8-36ce-4a8d-9bfe-139f7cab5f30
guest_dashboard_text_chat.js:671 Sent configure_tools message
guest_dashboard_text_chat.js:374 checkAndEnableChatButton called
guest_dashboard_text_chat.js:375 - idToken: Present (hidden)
guest_dashboard_text_chat.js:376 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:377 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:378 - window.confirmedPropertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:449 Both token and property ID are ready, chat can auto-start when needed
2guest_dashboard_text_chat.js:1012 Chat connection status: Connected
guest_dashboard_text_chat.js:694 Tools configuration response: Object
guest_dashboard_text_chat.js:37 Retrieved conversation history: Object
guest_dashboard_text_chat.js:647 Sent auth message with conversation history: 4 previous conversations and 5 current messages
guest_dashboard_text_chat.js:685 Authentication successful: Object
guest_dashboard_text_chat.js:959 Chat auto-started successfully, sending pending message
guest_dashboard_text_chat.js:967 Sent pending message via Socket.IO: how many night is my stay going to be? for property: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:786 Updated last activity timestamp
guest_dashboard_text_chat.js:760 Text message from AI via Socket.IO: Object
guest_dashboard_text_chat.js:786 Updated last activity timestamp
guest_dashboard_text_chat.js:177 Adding date separator for message: assistant 05:28 PM
dashboard:2075 addDateSeparatorIfNeeded: Using stored timestamp 2025-06-25T22:28:17.939Z -> Wed Jun 25 2025
dashboard:2096 addDateSeparatorIfNeeded: Previous message timestamp 2025-06-25T22:28:15.512Z -> Wed Jun 25 2025
dashboard:2123 No date separator needed for message
guest_dashboard_text_chat.js:800 Inactive for 2 minutes, closing WebSocket connection silently.
guest_dashboard_text_chat.js:1012 Chat connection status: Disconnected
guest_dashboard_text_chat.js:819 Sent inactivity disconnect notification to server
guest_dashboard_text_chat.js:716 Socket.IO connection CLOSED. Reason: io client disconnect
guest_dashboard_text_chat.js:1012 Chat connection status: Disconnected
guest_dashboard_text_chat.js:374 checkAndEnableChatButton called
guest_dashboard_text_chat.js:375 - idToken: Present (hidden)
guest_dashboard_text_chat.js:376 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:377 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:378 - window.confirmedPropertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:449 Both token and property ID are ready, chat can auto-start when needed
2guest_dashboard_text_chat.js:1012 Chat connection status: Disconnected
guest_dashboard_text_chat.js:728 This was an intentional disconnect, not showing reconnection message
guest_dashboard_text_chat.js:828 Socket.IO connection disconnected due to inactivity